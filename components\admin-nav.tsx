"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { auth } from "@/lib/firebase"
import {
  LayoutDashboard,
  Newspaper,
  FileText,
  Users,
  UserPlus,
  Upload,
  Settings,
  LogOut,
  Home,
  UserCheck,
  ClipboardList,
  Shield,
  Phone,
  Database,
  ActivityIcon as ActivityLog,
  WifiOff,
} from "lucide-react"
import { PermissionGate } from "./permission-gate"
import { PERMISSIONS } from "@/lib/permissions"

export function AdminNav() {
  const pathname = usePathname()

  const menuItems = [
    {
      title: "Tableau de bord",
      href: "/admin",
      icon: <LayoutDashboard className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.ADMIN],
    },
    {
      title: "Actualités",
      href: "/admin/news",
      icon: <Newspaper className="mr-2 h-4 w-4" />,
      permissions: [
        PERMISSIONS.READ_NEWS,
        PERMISSIONS.CREATE_NEWS,
        PERMISSIONS.UPDATE_NEWS,
        PERMISSIONS.DELETE_NEWS,
        PERMISSIONS.ADMIN,
      ],
    },
    {
      title: "Pages",
      href: "/admin/pages",
      icon: <FileText className="mr-2 h-4 w-4" />,
      permissions: [
        PERMISSIONS.READ_PAGES,
        PERMISSIONS.CREATE_PAGES,
        PERMISSIONS.UPDATE_PAGES,
        PERMISSIONS.DELETE_PAGES,
        PERMISSIONS.ADMIN,
      ],
    },
    {
      title: "Commerciaux",
      href: "/admin/commerciaux",
      icon: <Phone className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.ADMIN, PERMISSIONS.READ_USERS, PERMISSIONS.UPDATE_USERS],
    },
    {
      title: "Utilisateurs",
      href: "/admin/users",
      icon: <Users className="mr-2 h-4 w-4" />,
      permissions: [
        PERMISSIONS.READ_USERS,
        PERMISSIONS.CREATE_USERS,
        PERMISSIONS.UPDATE_USERS,
        PERMISSIONS.DELETE_USERS,
        PERMISSIONS.ADMIN,
      ],
    },
    {
      title: "Utilisateurs en attente",
      href: "/admin/users/pending",
      icon: <UserCheck className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.READ_USERS, PERMISSIONS.UPDATE_USERS, PERMISSIONS.ADMIN],
    },
    {
      title: "Utilisateurs pré-enregistrés",
      href: "/admin/pre-registered",
      icon: <ClipboardList className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.READ_USERS, PERMISSIONS.CREATE_USERS, PERMISSIONS.ADMIN],
    },
    {
      title: "Groupes",
      href: "/admin/groups",
      icon: <UserPlus className="mr-2 h-4 w-4" />,
      permissions: [
        PERMISSIONS.READ_GROUPS,
        PERMISSIONS.CREATE_GROUPS,
        PERMISSIONS.UPDATE_GROUPS,
        PERMISSIONS.DELETE_GROUPS,
        PERMISSIONS.ADMIN,
      ],
    },
    {
      title: "Rôles",
      href: "/admin/roles",
      icon: <Shield className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.ADMIN],
    },
    {
      title: "Import CSV",
      href: "/admin/import",
      icon: <Upload className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.MANAGE_IMPORT, PERMISSIONS.ADMIN],
    },
    {
      title: "Paramètres",
      href: "/admin/settings",
      icon: <Settings className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.READ_SETTINGS, PERMISSIONS.UPDATE_SETTINGS, PERMISSIONS.ADMIN],
    },
    {
      title: "Outils de diagnostic",
      href: "/admin/diagnostics",
      icon: <ActivityLog className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.ADMIN],
    },
    {
      title: "Gestion du cache",
      href: "/admin/cache",
      icon: <Database className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.ADMIN],
    },
    {
      title: "Mode hors ligne",
      href: "/admin/offline",
      icon: <WifiOff className="mr-2 h-4 w-4" />,
      permissions: [PERMISSIONS.ADMIN],
    },
  ]

  return (
    <nav className="grid items-start gap-1 py-2">
      <Link href="/dashboard">
        <Button
          variant="outline"
          className="w-full justify-start mb-2 border-primary/20 hover:bg-transparent hover:text-primary hover:underline text-sm dark:border-primary/30"
          size="sm"
        >
          <Home className="mr-1.5 h-4 w-4" />
          Retour à l'accueil
        </Button>
      </Link>

      {menuItems.map((item, index) => (
        <PermissionGate key={index} permissions={item.permissions} anyPermission={true}>
          <Link href={item.href}>
            <Button
              variant={pathname === item.href ? "secondary" : "ghost"}
              className={cn(
                "w-full justify-start text-sm py-1.5",
                pathname === item.href
                  ? "bg-primary/10 text-primary font-medium dark:bg-primary/20 dark:text-white"
                  : "hover:bg-transparent hover:text-foreground hover:underline",
              )}
              size="sm"
            >
              {item.icon}
              <span className="truncate">{item.title}</span>
            </Button>
          </Link>
        </PermissionGate>
      ))}

      <Button
        variant="ghost"
        className="mt-4 w-full justify-start text-red-500 hover:bg-transparent hover:text-red-600 hover:underline dark:text-red-400 dark:hover:text-red-300 text-sm"
        onClick={() => auth.signOut()}
        size="sm"
      >
        <LogOut className="mr-1.5 h-4 w-4" />
        Déconnexion
      </Button>
    </nav>
  )
}
