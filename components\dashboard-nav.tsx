"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton" // Assurez-vous d'avoir ce composant

import { useAuth } from "@/components/auth-provider"
import { useEffect, useState } from "react"
import Image from "next/image"
// Mettre à jour l'import des icônes pour ajouter UserCog
import { Newspaper, Star, Phone, User } from "lucide-react"
// Ajouter l'import de Permission
import type { Permission } from "@/lib/permissions"
// Add the import for the useFavoritesStatus hook
import { useFavoritesStatus } from "@/lib/hooks/use-favorites-status"
// Ajouter l'import du bouton d'installation
import { InstallButton } from "@/components/install-button"

// Mettre à jour l'interface DashboardNavProps pour inclure permissionRequired
interface DashboardNavProps {
  items?: {
    title: string
    href: string
    icon?: React.ReactNode
    iconUrl?: string | null
    permissionRequired?: string
  }[]
}

// Modifier le tableau defaultStaticMenuItems pour supprimer l'élément de profil utilisateur
// Remplacer le tableau defaultStaticMenuItems actuel par celui-ci:
const defaultStaticMenuItems = [
  {
    id: "dashboard",
    title: "Fil d'actualité",
    href: "/dashboard",
    icon: <Newspaper className="h-5 w-5" />,
  },
  {
    id: "favorites",
    title: "Mes favoris",
    href: "/dashboard/favorites",
    icon: <Star className="h-4 w-4" />,
  },
  {
    id: "commercial",
    title: "Contact commercial",
    href: "/dashboard/commercial",
    icon: <Phone className="h-4 w-4" />,
  },
  {
    id: "commercial-profile",
    title: "Mon profil commercial",
    href: "/dashboard/commercial-profile",
    icon: <User className="h-4 w-4" />,
    permissionRequired: "update:commercials",
  },
]

// Modifier la fonction DashboardNav pour filtrer les éléments en fonction des permissions

// Update the DashboardNav function to use the hook
export function DashboardNav({ items = [] }: DashboardNavProps) {
  const pathname = usePathname()
  const { user, isAdmin, menuItems, isMenuLoading, hasPermission } = useAuth()
  const { hasFavorites, isLoading: isFavoritesLoading } = useFavoritesStatus()
  const [allItems, setAllItems] = useState([...defaultStaticMenuItems])

  // Combiner les éléments par défaut avec les éléments de menu dynamiques
  useEffect(() => {
    if (menuItems && menuItems.length > 0) {
      // Filter the static items based on permissions and favorites status
      const filteredStaticItems = defaultStaticMenuItems.filter((item) => {
        // Skip the favorites menu item if the user has no favorites
        if (item.id === "favorites" && hasFavorites === false) {
          return false
        }
        return !item.permissionRequired || hasPermission(item.permissionRequired as Permission)
      })

      const dynamicItems = menuItems.map((item) => ({
        title: item.title,
        href: item.path,
        iconUrl: item.iconUrl,
      }))

      setAllItems([...filteredStaticItems, ...dynamicItems])
    } else {
      // Filter the static items based on permissions and favorites status
      const filteredStaticItems = defaultStaticMenuItems.filter((item) => {
        // Skip the favorites menu item if the user has no favorites
        if (item.id === "favorites" && hasFavorites === false) {
          return false
        }
        return !item.permissionRequired || hasPermission(item.permissionRequired as Permission)
      })

      setAllItems([...filteredStaticItems])
    }
  }, [menuItems, hasPermission, hasFavorites])

  if (isMenuLoading || isFavoritesLoading) {
    return (
      <div className="w-full">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="px-1 py-1">
            <Skeleton className="h-8 w-full" />
          </div>
        ))}
      </div>
    )
  }

  return (
    <ScrollArea className="my-4 h-[calc(100vh-8rem)]">
      <div className="flex flex-col gap-2 p-2">
        {allItems.map((item, index) => {
          const isActive = pathname === item.href
          const isDashboard = item.href === "/dashboard"

          return (
            <Button
              key={item.href}
              variant={isActive ? "secondary" : "ghost"}
              size={isDashboard ? "default" : "sm"}
              className={cn(
                "w-full min-w-0 transition-all duration-300",
                isDashboard && [
                  "dashboard-primary-nav",
                  "text-primary-foreground font-bold text-lg",
                  "border-0",
                  "mb-4 justify-center h-14",
                  "rounded-lg",
                  "overflow-hidden"
                ].join(" "),
                !isDashboard && "justify-start h-10",
                isActive && !isDashboard ? "bg-primary/10 text-primary font-medium hover:bg-primary/15" : "",
              )}
              asChild
            >
              <Link href={item.href} className={cn("flex items-center gap-3 min-w-0 relative z-10", isDashboard && "justify-center")}>
                {item.icon && (
                  <div className={cn(
                    "flex-shrink-0 transition-all duration-300",
                    isDashboard && "text-primary-foreground dashboard-primary-text"
                  )}>
                    {item.icon}
                  </div>
                )}
                {item.iconUrl && (
                  <div className="relative h-4 w-4 flex-shrink-0">
                    <Image
                      src={item.iconUrl || "/placeholder.svg"}
                      alt=""
                      width={16}
                      height={16}
                      className="h-4 w-4 object-contain"
                      onError={(e) => {
                        // En cas d'erreur, remplacer par une icône par défaut
                        e.currentTarget.src = "/placeholder.svg"
                      }}
                      unoptimized // Désactiver l'optimisation Next.js pour permettre le cache du service worker
                    />
                  </div>
                )}
                <span className={cn(
                  "truncate font-medium transition-all duration-300",
                  isDashboard && "text-center text-primary-foreground font-bold text-lg dashboard-primary-text"
                )}>
                  {item.title}
                </span>
              </Link>
            </Button>
          )
        })}
        <InstallButton className="mt-4" />
      </div>
    </ScrollArea>
  )
}
