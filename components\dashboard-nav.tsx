"use client"

import type React from "react"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton" // Assurez-vous d'avoir ce composant

import { useAuth } from "@/components/auth-provider"
import { useEffect, useState } from "react"
import Image from "next/image"
// Mettre à jour l'import des icônes pour ajouter UserCog
import { Newspaper, Star, Phone, User } from "lucide-react"
// Ajouter l'import de Permission
import type { Permission } from "@/lib/permissions"
// Add the import for the useFavoritesStatus hook
import { useFavoritesStatus } from "@/lib/hooks/use-favorites-status"
// Ajouter l'import du bouton d'installation
import { InstallButton } from "@/components/install-button"

// Mettre à jour l'interface DashboardNavProps pour inclure permissionRequired
interface DashboardNavProps {
  items?: {
    title: string
    href: string
    icon?: React.ReactNode
    iconUrl?: string | null
    permissionRequired?: string
  }[]
}

// Modifier le tableau defaultStaticMenuItems pour supprimer l'élément de profil utilisateur
// Remplacer le tableau defaultStaticMenuItems actuel par celui-ci:
const defaultStaticMenuItems = [
  {
    id: "dashboard",
    title: "Fil d'actualité",
    href: "/dashboard",
    icon: <Newspaper className="h-5 w-5" />,
  },
  {
    id: "favorites",
    title: "Mes favoris",
    href: "/dashboard/favorites",
    icon: <Star className="h-4 w-4" />,
  },
  {
    id: "commercial",
    title: "Contact commercial",
    href: "/dashboard/commercial",
    icon: <Phone className="h-4 w-4" />,
  },
  {
    id: "commercial-profile",
    title: "Mon profil commercial",
    href: "/dashboard/commercial-profile",
    icon: <User className="h-4 w-4" />,
    permissionRequired: "update:commercials",
  },
]

// Modifier la fonction DashboardNav pour filtrer les éléments en fonction des permissions

// Update the DashboardNav function to use the hook
export function DashboardNav({ items = [] }: DashboardNavProps) {
  const pathname = usePathname()
  const { user, isAdmin, menuItems, isMenuLoading, hasPermission } = useAuth()
  const { hasFavorites, isLoading: isFavoritesLoading } = useFavoritesStatus()
  const [allItems, setAllItems] = useState([...defaultStaticMenuItems])

  // Combiner les éléments par défaut avec les éléments de menu dynamiques
  useEffect(() => {
    if (menuItems && menuItems.length > 0) {
      // Filter the static items based on permissions and favorites status
      const filteredStaticItems = defaultStaticMenuItems.filter((item) => {
        // Skip the favorites menu item if the user has no favorites
        if (item.id === "favorites" && hasFavorites === false) {
          return false
        }
        return !item.permissionRequired || hasPermission(item.permissionRequired as Permission)
      })

      const dynamicItems = menuItems.map((item) => ({
        title: item.title,
        href: item.path,
        iconUrl: item.iconUrl,
      }))

      setAllItems([...filteredStaticItems, ...dynamicItems])
    } else {
      // Filter the static items based on permissions and favorites status
      const filteredStaticItems = defaultStaticMenuItems.filter((item) => {
        // Skip the favorites menu item if the user has no favorites
        if (item.id === "favorites" && hasFavorites === false) {
          return false
        }
        return !item.permissionRequired || hasPermission(item.permissionRequired as Permission)
      })

      setAllItems([...filteredStaticItems])
    }
  }, [menuItems, hasPermission, hasFavorites])

  if (isMenuLoading || isFavoritesLoading) {
    return (
      <div className="w-full">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="px-1 py-1">
            <Skeleton className="h-8 w-full" />
          </div>
        ))}
      </div>
    )
  }

  return (
    <ScrollArea className="my-4 h-[calc(100vh-8rem)]">
      <div className="flex flex-col gap-2 p-2">
        {allItems.map((item, index) => {
          const isActive = pathname === item.href
          const isDashboard = item.href === "/dashboard"

          return (
            <Button
              key={item.href}
              variant={isActive ? "secondary" : "ghost"}
              size={isDashboard ? "default" : "sm"}
              className={cn(
                "w-full min-w-0 transition-all duration-300 rounded-lg",
                // Primary Level - Fil d'actualité (most prominent but balanced)
                isDashboard && [
                  "nav-primary",
                  "bg-primary/10 hover:bg-primary/15 border border-primary/20 hover:border-primary/30",
                  "text-primary font-bold text-lg",
                  "mb-3 justify-center h-12",
                  "shadow-sm hover:shadow-md"
                ].join(" "),
                // Secondary Level - Contact commercial (high importance)
                !isDashboard && item.id === "commercial" && [
                  "nav-secondary",
                  "bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100",
                  "dark:from-blue-950/30 dark:to-indigo-950/30 dark:hover:from-blue-900/40 dark:hover:to-indigo-900/40",
                  "border border-blue-200/50 hover:border-blue-300/70 dark:border-blue-800/50 dark:hover:border-blue-700/70",
                  "text-blue-700 dark:text-blue-300 font-semibold",
                  "justify-start h-11 mb-2",
                  "shadow-sm hover:shadow-md"
                ].join(" "),
                // Tertiary Level - Commercial profile (medium importance)
                !isDashboard && item.id === "commercial-profile" && [
                  "nav-tertiary",
                  "bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100",
                  "dark:from-emerald-950/20 dark:to-teal-950/20 dark:hover:from-emerald-900/30 dark:hover:to-teal-900/30",
                  "border border-emerald-200/50 hover:border-emerald-300/70 dark:border-emerald-800/50 dark:hover:border-emerald-700/70",
                  "text-emerald-700 dark:text-emerald-300 font-medium",
                  "justify-start h-10 mb-1",
                  "shadow-sm hover:shadow"
                ].join(" "),
                // Standard navigation items (dynamic pages and favorites)
                !isDashboard && !["commercial", "commercial-profile"].includes(item.id || "") && [
                  "nav-standard",
                  "bg-muted/30 hover:bg-muted/60 border border-muted-foreground/10 hover:border-muted-foreground/20",
                  "text-foreground font-medium",
                  "justify-start h-10",
                  "hover:shadow-sm"
                ].join(" "),
                // Active state for non-dashboard items
                isActive && !isDashboard && "ring-2 ring-primary/20 bg-primary/5",
              )}
              asChild
            >
              <Link href={item.href} className={cn("flex items-center gap-3 min-w-0 relative z-10", isDashboard && "justify-center")}>
                {item.icon && (
                  <div className={cn(
                    "flex-shrink-0 transition-all duration-300",
                    isDashboard && "text-primary",
                    !isDashboard && item.id === "commercial" && "text-blue-600 dark:text-blue-400",
                    !isDashboard && item.id === "commercial-profile" && "text-emerald-600 dark:text-emerald-400",
                    !isDashboard && !["commercial", "commercial-profile"].includes(item.id || "") && "text-muted-foreground"
                  )}>
                    {item.icon}
                  </div>
                )}
                {item.iconUrl && (
                  <div className="relative h-4 w-4 flex-shrink-0">
                    <Image
                      src={item.iconUrl || "/placeholder.svg"}
                      alt=""
                      width={16}
                      height={16}
                      className="h-4 w-4 object-contain"
                      onError={(e) => {
                        // En cas d'erreur, remplacer par une icône par défaut
                        e.currentTarget.src = "/placeholder.svg"
                      }}
                      unoptimized // Désactiver l'optimisation Next.js pour permettre le cache du service worker
                    />
                  </div>
                )}
                <span className={cn(
                  "truncate transition-all duration-300",
                  isDashboard && "text-center text-primary font-bold text-lg",
                  !isDashboard && item.id === "commercial" && "text-blue-700 dark:text-blue-300 font-semibold",
                  !isDashboard && item.id === "commercial-profile" && "text-emerald-700 dark:text-emerald-300 font-medium",
                  !isDashboard && !["commercial", "commercial-profile"].includes(item.id || "") && "text-foreground font-medium"
                )}>
                  {item.title}
                </span>
              </Link>
            </Button>
          )
        })}

        {/* Utility Section - Separated from navigation */}
        <div className="mt-6 pt-4 border-t border-border/50">
          <div className="px-2">
            <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider mb-3 px-2">
              Utilitaires
            </p>
            <InstallButton className="w-full" />
          </div>
        </div>
      </div>
    </ScrollArea>
  )
}
