import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { AdminNav } from "@/components/admin-nav"
import { UserAccountNav } from "@/components/user-account-nav"
import { MobileNav } from "@/components/mobile-nav"
import { ThemeToggle } from "@/components/theme-toggle"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Administration | ACR Direct",
  description: "Gestion du portail ACR Direct",
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen flex-col bg-background">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container flex h-14 sm:h-16 items-center justify-between py-2 sm:py-4 px-2 sm:px-4 md:px-6">
          <div className="flex items-center gap-2 md:gap-4">
            <MobileNav isAdmin={true} />
            <Link href="/admin" className="flex items-center">
              <img src="/logo-acr-direct.png" alt="ACR Direct" className="h-10 w-auto rounded-lg mr-2" />
              <span className="font-bold hidden md:inline-block">Administration</span>
            </Link>
          </div>
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <UserAccountNav />
          </div>
        </div>
      </header>
      <div className="container grid flex-1 gap-12 md:grid-cols-[240px_1fr] px-0 sm:px-4 md:px-6">
        <aside className="hidden w-[240px] flex-col md:flex">
          <AdminNav />
        </aside>
        <main className="flex w-full flex-1 flex-col overflow-hidden py-6" id="main-content">
          {children}
        </main>
      </div>
    </div>
  )
}
