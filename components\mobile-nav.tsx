"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, User, LogOut } from "lucide-react"
import { DashboardNav } from "@/components/dashboard-nav"
import { AdminNav } from "@/components/admin-nav"
import { InstallButton } from "@/components/install-button"
import { useAuth } from "@/components/auth-provider"

interface MobileNavProps {
  isAdmin?: boolean
}

export function MobileNav({ isAdmin = false }: MobileNavProps) {
  const [open, setOpen] = useState(false)
  const pathname = usePathname()
  const { logout } = useAuth()

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden"
        >
          <Menu className="h-6 w-6" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0 bg-background">
        <div className="px-4 py-3 border-b flex items-center justify-between dark:border-gray-800">
          <Link href={isAdmin ? "/admin" : "/dashboard"} className="flex items-center" onClick={() => setOpen(false)}>
            <img src="/logo-acr-direct.png" alt="ACR Direct" className="h-10 w-auto rounded-lg mr-2" />
            <span className="font-bold">{isAdmin ? "Administration" : ""}</span>
          </Link>
          <div className="flex items-center gap-2 pr-8 md:hidden">
            <Link href="/dashboard/profile" onClick={() => setOpen(false)}>
              <Button variant="ghost" size="icon" className="h-8 w-8" aria-label="Mon profil">
                <User className="h-5 w-5" />
              </Button>
            </Link>
            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={logout} aria-label="Déconnexion">
              <LogOut className="h-5 w-5" />
            </Button>
          </div>
        </div>
        <div className="h-[calc(100vh-4rem)] overflow-y-auto pb-10 px-3 bg-background">
          {isAdmin ? (
            <div onClick={() => setOpen(false)} className="w-full overflow-visible">
              <AdminNav />
            </div>
          ) : (
            <div onClick={() => setOpen(false)} className="w-full overflow-visible">
              <DashboardNav />
            </div>
          )}
          <InstallButton variant="mobile" className="mt-2" />
        </div>
      </SheetContent>
    </Sheet>
  )
}
